#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script simple pour démarrer le bot maître
"""

import os
import sys
import logging
from config import MASTER_BOT_TOKEN, ADMIN_USER_ID, LOG_FORMAT, LOG_LEVEL, LOGS_DIR
from bot_manager import BotManager
from api_client import APIClient
import telebot

def main():
    """Démarrer le bot maître"""
    print("🤖 ===============================================")
    print("   BOT CRÉATEUR DE BOTS - DÉMARRAGE")
    print("===============================================")
    
    # Vérifier la configuration
    if not MASTER_BOT_TOKEN:
        print("❌ MASTER_BOT_TOKEN non configuré dans .env")
        return
    
    if not ADMIN_USER_ID:
        print("❌ ADMIN_USER_ID non configuré dans .env")
        return
    
    # Créer les dossiers nécessaires
    os.makedirs(LOGS_DIR, exist_ok=True)
    os.makedirs('bots', exist_ok=True)
    
    # Configuration du logging
    logging.basicConfig(
        level=getattr(logging, LOG_LEVEL),
        format=LOG_FORMAT,
        handlers=[
            logging.FileHandler(f'{LOGS_DIR}/master_bot.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    
    print("✅ Configuration validée")
    print(f"👤 Admin User ID: {ADMIN_USER_ID}")
    print("🚀 Démarrage du Bot Maître...")
    print("📱 Envoyez /start au bot @dev_souhail_bot pour commencer!")
    print("🛑 Appuyez sur Ctrl+C pour arrêter\n")
    
    # Initialisation
    bot = telebot.TeleBot(MASTER_BOT_TOKEN)
    bot_manager = BotManager()
    api_client = APIClient()
    
    def is_admin(user_id):
        """Vérifier si l'utilisateur est admin"""
        return str(user_id) == str(ADMIN_USER_ID)
    
    @bot.message_handler(commands=['start'])
    def start_command(message):
        """Commande de démarrage"""
        if not is_admin(message.from_user.id):
            bot.reply_to(message, "❌ Accès refusé. Seul l'administrateur peut utiliser ce bot.")
            return
        
        welcome_text = """🤖 *Bot Maître - Gestionnaire de Bots*

📋 *Commandes disponibles:*

*Gestion des bots:*
• /create\_bot <nom> <token> \[description\] - Créer un nouveau bot
• /list\_bots - Lister tous les bots
• /start\_bot <bot\_id> - Démarrer un bot
• /stop\_bot <bot\_id> - Arrêter un bot
• /delete\_bot <bot\_id> - Supprimer un bot
• /bot\_info <bot\_id> - Informations sur un bot

*APIs de test:*
• /test\_check <uid> - Tester l'API check\_status
• /test\_spam <uid> - Tester l'API spam
• /test\_events \[region\] - Tester l'API events
• /test\_info - Tester l'API info

• /help - Afficher cette aide"""
        
        bot.reply_to(message, welcome_text, parse_mode='Markdown')
    
    @bot.message_handler(commands=['help'])
    def help_command(message):
        """Commande d'aide"""
        if not is_admin(message.from_user.id):
            bot.reply_to(message, "❌ Accès refusé.")
            return
        start_command(message)
    
    @bot.message_handler(commands=['create_bot'])
    def create_bot_command(message):
        """Créer un nouveau bot"""
        if not is_admin(message.from_user.id):
            bot.reply_to(message, "❌ Accès refusé.")
            return
        
        try:
            args = message.text.split(maxsplit=3)
            if len(args) < 3:
                bot.reply_to(message, "❌ Usage: `/create_bot <nom> <token> [description]`", parse_mode='Markdown')
                return
            
            name = args[1]
            token = args[2]
            description = args[3] if len(args) > 3 else ""
            
            bot_id, result_message = bot_manager.create_bot(name, token, description)
            
            if bot_id:
                response = f"✅ *Bot créé avec succès !*\n\n"
                response += f"🆔 *ID:* `{bot_id}`\n"
                response += f"📝 *Nom:* {name}\n"
                response += f"📄 *Description:* {description}\n\n"
                response += f"Pour démarrer le bot: `/start_bot {bot_id}`"
            else:
                response = f"❌ *Erreur:* {result_message}"
            
            bot.reply_to(message, response, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Erreur dans create_bot_command: {e}")
            bot.reply_to(message, f"❌ Erreur lors de la création du bot: {e}")
    
    @bot.message_handler(commands=['list_bots'])
    def list_bots_command(message):
        """Lister tous les bots"""
        if not is_admin(message.from_user.id):
            bot.reply_to(message, "❌ Accès refusé.")
            return
        
        try:
            bots = bot_manager.list_bots()
            
            if not bots:
                bot.reply_to(message, "📭 Aucun bot créé pour le moment.")
                return
            
            response = f"🤖 *Liste des bots ({len(bots)}):*\n\n"
            
            for bot_id, bot_data in bots.items():
                status_emoji = "🟢" if bot_data['status'] == 'running' else "🔴"
                response += f"{status_emoji} *{bot_data['name']}*\n"
                response += f"   🆔 ID: `{bot_id[:8]}...`\n"
                response += f"   📊 Statut: {bot_data['status']}\n"
                response += f"   📅 Créé: {bot_data['created_at'][:10]}\n\n"
            
            bot.reply_to(message, response, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Erreur dans list_bots_command: {e}")
            bot.reply_to(message, f"❌ Erreur lors de la récupération de la liste: {e}")
    
    @bot.message_handler(commands=['test_info'])
    def test_info_command(message):
        """Tester l'API info"""
        if not is_admin(message.from_user.id):
            bot.reply_to(message, "❌ Accès refusé.")
            return
        
        try:
            result = api_client.get_info()
            
            if result:
                response = f"✅ *Test API info réussi*\n\n"
                response += f"📊 *Résultat:*\n```json\n{result}```"
            else:
                response = f"❌ *Test API info échoué*\n\nImpossible de récupérer les informations"
            
            bot.reply_to(message, response, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Erreur dans test_info_command: {e}")
            bot.reply_to(message, f"❌ Erreur lors du test: {e}")
    
    # Démarrer le bot
    logger.info("Démarrage du Bot Maître")
    logger.info(f"Admin User ID: {ADMIN_USER_ID}")
    
    try:
        bot.infinity_polling()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du système...")
        logger.info("Arrêt du Bot Maître")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        logger.error(f"Erreur fatale: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
