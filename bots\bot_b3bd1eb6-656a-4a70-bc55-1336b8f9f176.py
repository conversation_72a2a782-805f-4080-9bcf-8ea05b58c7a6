#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bot secondaire généré automatiquement
Nom: souhail
ID: b3bd1eb6-656a-4a70-bc55-1336b8f9f176
Description: souhail oukili
"""

import telebot
import logging
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api_client import APIClient

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/bot_b3bd1eb6-656a-4a70-bc55-1336b8f9f176.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Initialisation du bot
bot = telebot.TeleBot('7584443487:AAGfjko-ecgh-XxFD72AjSWDvlyjPOuGUDY')
api_client = APIClient()

@bot.message_handler(commands=['start'])
def start_command(message):
    """Commande de démarrage"""
    welcome_text = f"""
🤖 Bonjour ! Je suis souhail

📋 Fonctionnalités disponibles:
"""
    
    if 'check_status' in ['check_status', 'spam', 'events', 'info']:
        welcome_text += "• /check <uid> - Vérifier le statut d'un utilisateur\n"
    if 'spam' in ['check_status', 'spam', 'events', 'info']:
        welcome_text += "• /spam <uid> - Envoyer un spam à un utilisateur\n"
    if 'events' in ['check_status', 'spam', 'events', 'info']:
        welcome_text += "• /events [region] - Récupérer les événements\n"
    if 'info' in ['check_status', 'spam', 'events', 'info']:
        welcome_text += "• /info - Récupérer les informations générales\n"
    
    welcome_text += "• /help - Afficher cette aide"
    
    bot.reply_to(message, welcome_text)

@bot.message_handler(commands=['help'])
def help_command(message):
    """Commande d'aide"""
    start_command(message)

if 'check_status' in ['check_status', 'spam', 'events', 'info']:
    @bot.message_handler(commands=['check'])
    def check_status_command(message):
        """Vérifier le statut d'un utilisateur"""
        try:
            args = message.text.split()
            if len(args) < 2:
                bot.reply_to(message, "❌ Usage: /check <uid>")
                return
            
            uid = args[1]
            result = api_client.check_status(uid)
            
            if result:
                bot.reply_to(message, f"✅ Statut pour UID {uid}:\n```json\n{result}```", parse_mode='Markdown')
            else:
                bot.reply_to(message, f"❌ Impossible de récupérer le statut pour UID {uid}")
        except Exception as e:
            logger.error(f"Erreur dans check_status_command: {e}")
            bot.reply_to(message, "❌ Erreur lors de la vérification du statut")

if 'spam' in ['check_status', 'spam', 'events', 'info']:
    @bot.message_handler(commands=['spam'])
    def spam_command(message):
        """Envoyer un spam à un utilisateur"""
        try:
            args = message.text.split()
            if len(args) < 2:
                bot.reply_to(message, "❌ Usage: /spam <uid>")
                return
            
            uid = args[1]
            result = api_client.spam_user(uid)
            
            if result:
                bot.reply_to(message, f"✅ Spam envoyé à UID {uid}:\n```json\n{result}```", parse_mode='Markdown')
            else:
                bot.reply_to(message, f"❌ Impossible d'envoyer le spam à UID {uid}")
        except Exception as e:
            logger.error(f"Erreur dans spam_command: {e}")
            bot.reply_to(message, "❌ Erreur lors de l'envoi du spam")

if 'events' in ['check_status', 'spam', 'events', 'info']:
    @bot.message_handler(commands=['events'])
    def events_command(message):
        """Récupérer les événements"""
        try:
            args = message.text.split()
            region = args[1] if len(args) > 1 else 'ME'
            
            result = api_client.get_events(region)
            
            if result:
                bot.reply_to(message, f"📅 Événements pour la région {region}:\n```json\n{result}```", parse_mode='Markdown')
            else:
                bot.reply_to(message, f"❌ Impossible de récupérer les événements pour la région {region}")
        except Exception as e:
            logger.error(f"Erreur dans events_command: {e}")
            bot.reply_to(message, "❌ Erreur lors de la récupération des événements")

if 'info' in ['check_status', 'spam', 'events', 'info']:
    @bot.message_handler(commands=['info'])
    def info_command(message):
        """Récupérer les informations générales"""
        try:
            result = api_client.get_info()
            
            if result:
                bot.reply_to(message, f"ℹ️ Informations générales:\n```json\n{result}```", parse_mode='Markdown')
            else:
                bot.reply_to(message, "❌ Impossible de récupérer les informations")
        except Exception as e:
            logger.error(f"Erreur dans info_command: {e}")
            bot.reply_to(message, "❌ Erreur lors de la récupération des informations")

if __name__ == '__main__':
    logger.info(f"Démarrage du bot {bot_data['name']} (ID: {bot_data['id']})")
    try:
        bot.infinity_polling()
    except Exception as e:
        logger.error(f"Erreur fatale: {e}")
