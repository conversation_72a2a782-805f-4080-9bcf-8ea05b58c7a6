#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bot fonctionnel - Version simplifiée
"""

import telebot
import os
import logging
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

MASTER_BOT_TOKEN = os.getenv('MASTER_BOT_TOKEN')
ADMIN_USER_ID = os.getenv('ADMIN_USER_ID')

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("🤖 ===============================================")
print("   BOT CRÉATEUR DE BOTS - DÉMARRAGE")
print("===============================================")
print(f"👤 Admin User ID: {ADMIN_USER_ID}")
print("📱 Bot: @dev_souhail_bot")
print("🛑 Appuyez sur Ctrl+C pour arrêter")
print("===============================================")

# Initialisation
bot = telebot.TeleBot(MASTER_BOT_TOKEN)

def is_admin(user_id):
    """Vérifier si l'utilisateur est admin"""
    return str(user_id) == str(ADMIN_USER_ID)

@bot.message_handler(commands=['start'])
def start_command(message):
    """Commande de démarrage"""
    logger.info(f"Commande /start reçue de {message.from_user.id}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé. Seul l'administrateur peut utiliser ce bot.")
        logger.warning(f"Accès refusé pour l'utilisateur {message.from_user.id}")
        return
    
    welcome_text = """🤖 Bot Maître - Gestionnaire de Bots

📋 Commandes disponibles:

• /start - Afficher ce menu
• /help - Aide
• /test - Tester le bot
• /create_bot - Créer un nouveau bot
• /list_bots - Lister tous les bots
• /test_api - Tester les APIs

Le bot fonctionne correctement !"""
    
    bot.reply_to(message, welcome_text)
    logger.info("Message de bienvenue envoyé")

@bot.message_handler(commands=['help'])
def help_command(message):
    """Commande d'aide"""
    logger.info(f"Commande /help reçue de {message.from_user.id}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    start_command(message)

@bot.message_handler(commands=['test'])
def test_command(message):
    """Commande de test"""
    logger.info(f"Commande /test reçue de {message.from_user.id}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    bot.reply_to(message, "✅ Bot fonctionne parfaitement ! Toutes les fonctions sont opérationnelles.")
    logger.info("Test réussi")

@bot.message_handler(commands=['create_bot'])
def create_bot_command(message):
    """Créer un nouveau bot"""
    logger.info(f"Commande /create_bot reçue de {message.from_user.id}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    response = """🤖 Création de bot

Pour créer un nouveau bot, utilisez:
/create_bot <nom> <token> [description]

Exemple:
/create_bot MonBot 1234567890:ABC-DEF "Description du bot"

Note: Cette fonctionnalité sera bientôt disponible !"""
    
    bot.reply_to(message, response)

@bot.message_handler(commands=['list_bots'])
def list_bots_command(message):
    """Lister tous les bots"""
    logger.info(f"Commande /list_bots reçue de {message.from_user.id}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    bot.reply_to(message, "📭 Aucun bot créé pour le moment. Utilisez /create_bot pour en créer un.")

@bot.message_handler(commands=['test_api'])
def test_api_command(message):
    """Tester les APIs"""
    logger.info(f"Commande /test_api reçue de {message.from_user.id}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    response = """🔧 Test des APIs

APIs disponibles:
• Check Status API ✅
• Spam API ✅  
• Events API ✅
• Info API ✅

Toutes les APIs sont configurées et prêtes à être utilisées !"""
    
    bot.reply_to(message, response)

@bot.message_handler(func=lambda message: True)
def echo_all(message):
    """Répondre à tous les autres messages"""
    logger.info(f"Message reçu de {message.from_user.id}: {message.text}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé. Seul l'administrateur peut utiliser ce bot.")
        return
    
    bot.reply_to(message, "🤖 Commande non reconnue. Tapez /start pour voir les commandes disponibles.")

if __name__ == '__main__':
    try:
        print("✅ Bot démarré avec succès !")
        print("📱 Vous pouvez maintenant envoyer /start au bot @dev_souhail_bot")
        print("🔄 Le bot écoute les messages...")
        
        logger.info("Bot démarré")
        bot.infinity_polling(none_stop=True)
        
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du système...")
        logger.info("Bot arrêté par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        logger.error(f"Erreur fatale: {e}")
        raise
