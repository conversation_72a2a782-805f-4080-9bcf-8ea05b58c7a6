#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bot simple pour test
"""

import telebot
import os
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

MASTER_BOT_TOKEN = os.getenv('MASTER_BOT_TOKEN')
ADMIN_USER_ID = os.getenv('ADMIN_USER_ID')

print("🤖 Démarrage du Bot Maître...")
print(f"👤 Admin User ID: {ADMIN_USER_ID}")
print("📱 Bot: @dev_souhail_bot")
print("🛑 Appuyez sur Ctrl+C pour arrêter\n")

# Initialisation
bot = telebot.TeleBot(MASTER_BOT_TOKEN)

def is_admin(user_id):
    """Vérifier si l'utilisateur est admin"""
    return str(user_id) == str(ADMIN_USER_ID)

@bot.message_handler(commands=['start'])
def start_command(message):
    """Commande de démarrage"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé. Seul l'administrateur peut utiliser ce bot.")
        return
    
    welcome_text = """🤖 Bot Maître - Gestionnaire de Bots

📋 Commandes disponibles:

Gestion des bots:
• /create_bot <nom> <token> [description] - Créer un nouveau bot
• /list_bots - Lister tous les bots
• /start_bot <bot_id> - Démarrer un bot
• /stop_bot <bot_id> - Arrêter un bot
• /delete_bot <bot_id> - Supprimer un bot
• /bot_info <bot_id> - Informations sur un bot

APIs de test:
• /test_check <uid> - Tester l'API check_status
• /test_spam <uid> - Tester l'API spam
• /test_events [region] - Tester l'API events
• /test_info - Tester l'API info

• /help - Afficher cette aide"""
    
    bot.reply_to(message, welcome_text)

@bot.message_handler(commands=['help'])
def help_command(message):
    """Commande d'aide"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    start_command(message)

@bot.message_handler(commands=['test'])
def test_command(message):
    """Commande de test"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    bot.reply_to(message, "✅ Bot fonctionne correctement !")

if __name__ == '__main__':
    try:
        print("✅ Bot démarré avec succès !")
        print("📱 Vous pouvez maintenant envoyer /start au bot @dev_souhail_bot")
        bot.infinity_polling()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du système...")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
