import json
import uuid
import threading
import subprocess
import os
import logging
from datetime import datetime
from config import BOT_DATA_FILE, MAX_BOTS, LOGS_DIR

logger = logging.getLogger(__name__)

class BotManager:
    """Gestionnaire des bots secondaires"""
    
    def __init__(self):
        self.bots = {}
        self.load_bots_data()
    
    def load_bots_data(self):
        """Charger les données des bots depuis le fichier"""
        try:
            if os.path.exists(BOT_DATA_FILE):
                with open(BOT_DATA_FILE, 'r', encoding='utf-8') as f:
                    self.bots = json.load(f)
            logger.info(f"Chargé {len(self.bots)} bots depuis le fichier")
        except Exception as e:
            logger.error(f"Erreur lors du chargement des données des bots: {e}")
            self.bots = {}
    
    def save_bots_data(self):
        """Sauvegarder les données des bots dans le fichier"""
        try:
            with open(BOT_DATA_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.bots, f, indent=2, ensure_ascii=False)
            logger.info("Données des bots sauvegardées")
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde des données des bots: {e}")
    
    def create_bot(self, name, token, description="", features=None):
        """Créer un nouveau bot"""
        if len(self.bots) >= MAX_BOTS:
            return None, "Nombre maximum de bots atteint"
        
        if features is None:
            features = ["check_status", "spam", "events", "info"]
        
        bot_id = str(uuid.uuid4())
        bot_data = {
            'id': bot_id,
            'name': name,
            'token': token,
            'description': description,
            'features': features,
            'status': 'stopped',
            'created_at': datetime.now().isoformat(),
            'process_id': None,
            'log_file': f"{LOGS_DIR}/bot_{bot_id}.log"
        }
        
        # Créer le fichier du bot
        bot_file = f"bots/bot_{bot_id}.py"
        self.create_bot_file(bot_file, bot_data)
        
        self.bots[bot_id] = bot_data
        self.save_bots_data()
        
        logger.info(f"Bot créé: {name} (ID: {bot_id})")
        return bot_id, "Bot créé avec succès"
    
    def create_bot_file(self, file_path, bot_data):
        """Créer le fichier Python pour un bot secondaire"""
        bot_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bot secondaire généré automatiquement
Nom: {bot_data['name']}
ID: {bot_data['id']}
Description: {bot_data['description']}
"""

import telebot
import logging
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api_client import APIClient

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('{bot_data['log_file']}', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Initialisation du bot
bot = telebot.TeleBot('{bot_data['token']}')
api_client = APIClient()

@bot.message_handler(commands=['start'])
def start_command(message):
    """Commande de démarrage"""
    welcome_text = f"""
🤖 Bonjour ! Je suis {bot_data['name']}

📋 Fonctionnalités disponibles:
"""
    
    if 'check_status' in {bot_data['features']}:
        welcome_text += "• /check <uid> - Vérifier le statut d'un utilisateur\\n"
    if 'spam' in {bot_data['features']}:
        welcome_text += "• /spam <uid> - Envoyer un spam à un utilisateur\\n"
    if 'events' in {bot_data['features']}:
        welcome_text += "• /events [region] - Récupérer les événements\\n"
    if 'info' in {bot_data['features']}:
        welcome_text += "• /info - Récupérer les informations générales\\n"
    
    welcome_text += "• /help - Afficher cette aide"
    
    bot.reply_to(message, welcome_text)

@bot.message_handler(commands=['help'])
def help_command(message):
    """Commande d'aide"""
    start_command(message)

if 'check_status' in {bot_data['features']}:
    @bot.message_handler(commands=['check'])
    def check_status_command(message):
        """Vérifier le statut d'un utilisateur"""
        try:
            args = message.text.split()
            if len(args) < 2:
                bot.reply_to(message, "❌ Usage: /check <uid>")
                return
            
            uid = args[1]
            result = api_client.check_status(uid)
            
            if result:
                bot.reply_to(message, f"✅ Statut pour UID {{uid}}:\\n```json\\n{{result}}```", parse_mode='Markdown')
            else:
                bot.reply_to(message, f"❌ Impossible de récupérer le statut pour UID {{uid}}")
        except Exception as e:
            logger.error(f"Erreur dans check_status_command: {{e}}")
            bot.reply_to(message, "❌ Erreur lors de la vérification du statut")

if 'spam' in {bot_data['features']}:
    @bot.message_handler(commands=['spam'])
    def spam_command(message):
        """Envoyer un spam à un utilisateur"""
        try:
            args = message.text.split()
            if len(args) < 2:
                bot.reply_to(message, "❌ Usage: /spam <uid>")
                return
            
            uid = args[1]
            result = api_client.spam_user(uid)
            
            if result:
                bot.reply_to(message, f"✅ Spam envoyé à UID {{uid}}:\\n```json\\n{{result}}```", parse_mode='Markdown')
            else:
                bot.reply_to(message, f"❌ Impossible d'envoyer le spam à UID {{uid}}")
        except Exception as e:
            logger.error(f"Erreur dans spam_command: {{e}}")
            bot.reply_to(message, "❌ Erreur lors de l'envoi du spam")

if 'events' in {bot_data['features']}:
    @bot.message_handler(commands=['events'])
    def events_command(message):
        """Récupérer les événements"""
        try:
            args = message.text.split()
            region = args[1] if len(args) > 1 else 'ME'
            
            result = api_client.get_events(region)
            
            if result:
                bot.reply_to(message, f"📅 Événements pour la région {{region}}:\\n```json\\n{{result}}```", parse_mode='Markdown')
            else:
                bot.reply_to(message, f"❌ Impossible de récupérer les événements pour la région {{region}}")
        except Exception as e:
            logger.error(f"Erreur dans events_command: {{e}}")
            bot.reply_to(message, "❌ Erreur lors de la récupération des événements")

if 'info' in {bot_data['features']}:
    @bot.message_handler(commands=['info'])
    def info_command(message):
        """Récupérer les informations générales"""
        try:
            result = api_client.get_info()
            
            if result:
                bot.reply_to(message, f"ℹ️ Informations générales:\\n```json\\n{{result}}```", parse_mode='Markdown')
            else:
                bot.reply_to(message, "❌ Impossible de récupérer les informations")
        except Exception as e:
            logger.error(f"Erreur dans info_command: {{e}}")
            bot.reply_to(message, "❌ Erreur lors de la récupération des informations")

if __name__ == '__main__':
    logger.info(f"Démarrage du bot {{bot_data['name']}} (ID: {{bot_data['id']}})")
    try:
        bot.infinity_polling()
    except Exception as e:
        logger.error(f"Erreur fatale: {{e}}")
'''
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(bot_code)
    
    def start_bot(self, bot_id):
        """Démarrer un bot"""
        if bot_id not in self.bots:
            return False, "Bot non trouvé"
        
        bot_data = self.bots[bot_id]
        if bot_data['status'] == 'running':
            return False, "Bot déjà en cours d'exécution"
        
        try:
            bot_file = f"bots/bot_{bot_id}.py"
            process = subprocess.Popen([
                'python', bot_file
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            bot_data['process_id'] = process.pid
            bot_data['status'] = 'running'
            self.save_bots_data()
            
            logger.info(f"Bot {bot_data['name']} démarré (PID: {process.pid})")
            return True, f"Bot démarré avec succès (PID: {process.pid})"
        except Exception as e:
            logger.error(f"Erreur lors du démarrage du bot {bot_id}: {e}")
            return False, f"Erreur lors du démarrage: {e}"
    
    def stop_bot(self, bot_id):
        """Arrêter un bot"""
        if bot_id not in self.bots:
            return False, "Bot non trouvé"
        
        bot_data = self.bots[bot_id]
        if bot_data['status'] != 'running':
            return False, "Bot non en cours d'exécution"
        
        try:
            if bot_data['process_id']:
                os.kill(bot_data['process_id'], 9)  # Force kill
            
            bot_data['process_id'] = None
            bot_data['status'] = 'stopped'
            self.save_bots_data()
            
            logger.info(f"Bot {bot_data['name']} arrêté")
            return True, "Bot arrêté avec succès"
        except Exception as e:
            logger.error(f"Erreur lors de l'arrêt du bot {bot_id}: {e}")
            return False, f"Erreur lors de l'arrêt: {e}"
    
    def delete_bot(self, bot_id):
        """Supprimer un bot"""
        if bot_id not in self.bots:
            return False, "Bot non trouvé"
        
        # Arrêter le bot s'il est en cours d'exécution
        if self.bots[bot_id]['status'] == 'running':
            self.stop_bot(bot_id)
        
        try:
            # Supprimer le fichier du bot
            bot_file = f"bots/bot_{bot_id}.py"
            if os.path.exists(bot_file):
                os.remove(bot_file)
            
            # Supprimer le fichier de log
            log_file = self.bots[bot_id]['log_file']
            if os.path.exists(log_file):
                os.remove(log_file)
            
            # Supprimer de la liste
            del self.bots[bot_id]
            self.save_bots_data()
            
            logger.info(f"Bot {bot_id} supprimé")
            return True, "Bot supprimé avec succès"
        except Exception as e:
            logger.error(f"Erreur lors de la suppression du bot {bot_id}: {e}")
            return False, f"Erreur lors de la suppression: {e}"
    
    def get_bot_info(self, bot_id):
        """Récupérer les informations d'un bot"""
        return self.bots.get(bot_id)
    
    def list_bots(self):
        """Lister tous les bots"""
        return self.bots
