# 🤖 Guide d'Utilisation - Bot Créateur de Bots

## 🚀 Démarrage Rapide

### 1. <PERSON><PERSON><PERSON><PERSON> le Bot Ma<PERSON>
```bash
python start.py
```
ou directement :
```bash
python master_bot.py
```

### 2. Accéder au Bot
- Ouvrez Telegram
- Recherchez `@dev_souhail_bot`
- Envoyez `/start` pour voir le menu

## 📋 Commandes Principales

### 🤖 Gestion des Bots

#### Créer un nouveau bot
```
/create_bot <nom> <token> [description]
```
**Exemple :**
```
/create_bot MonPremierBot 1234567890:ABCdefGHIjklMNOpqrsTUVwxyz "Bot de test pour les utilisateurs"
```

#### Lister tous les bots
```
/list_bots
```
Affiche tous les bots créés avec leur statut (🟢 actif / 🔴 arrêté)

#### Démarrer un bot
```
/start_bot <bot_id>
```
**Exemple :**
```
/start_bot a1b2c3d4-e5f6-7890-abcd-ef1234567890
```

#### Arrêter un bot
```
/stop_bot <bot_id>
```

#### Supprimer un bot
```
/delete_bot <bot_id>
```
⚠️ **Attention :** Cette action est irréversible !

#### Informations détaillées d'un bot
```
/bot_info <bot_id>
```

### 🧪 Test des APIs

#### Tester l'API Check Status
```
/test_check <uid>
```
**Exemple :**
```
/test_check 123456789
```

#### Tester l'API Spam
```
/test_spam <uid>
```
**Exemple :**
```
/test_spam 123456789
```

#### Tester l'API Events
```
/test_events [region]
```
**Exemples :**
```
/test_events
/test_events US
/test_events EU
```

#### Tester l'API Info
```
/test_info
```

## 🔧 Fonctionnalités des Bots Créés

Chaque bot créé aura automatiquement ces commandes :

### Pour les utilisateurs du bot secondaire :
- `/start` - Menu d'accueil
- `/help` - Aide
- `/check <uid>` - Vérifier le statut d'un utilisateur
- `/spam <uid>` - Envoyer un spam à un utilisateur
- `/events [region]` - Récupérer les événements
- `/info` - Informations générales

## 📊 Exemple Complet d'Utilisation

### Étape 1 : Créer un bot
```
Vous: /create_bot TestBot 1234567890:ABCdefGHIjklMNOpqrsTUVwxyz "Bot de démonstration"

Bot: ✅ Bot créé avec succès !

🆔 ID: a1b2c3d4-e5f6-7890-abcd-ef1234567890
📝 Nom: TestBot
📄 Description: Bot de démonstration

Pour démarrer le bot: /start_bot a1b2c3d4-e5f6-7890-abcd-ef1234567890
```

### Étape 2 : Démarrer le bot
```
Vous: /start_bot a1b2c3d4-e5f6-7890-abcd-ef1234567890

Bot: ✅ Bot démarré !

🤖 Nom: TestBot
🆔 ID: a1b2c3d4...
📊 Statut: En cours d'exécution
ℹ️ Bot démarré avec succès (PID: 12345)
```

### Étape 3 : Vérifier le statut
```
Vous: /list_bots

Bot: 🤖 Liste des bots (1):

🟢 TestBot
   🆔 ID: a1b2c3d4...
   📊 Statut: running
   📅 Créé: 2025-07-27
```

### Étape 4 : Tester les APIs
```
Vous: /test_check 123456789

Bot: ✅ Test API check_status réussi

🆔 UID: 123456789
📊 Résultat:
```json
{"status": "active", "uid": "123456789"}
```
```

## 🔒 Sécurité

- **Accès restreint** : Seul l'administrateur (ID: 7763623565) peut utiliser le bot maître
- **Isolation des processus** : Chaque bot secondaire fonctionne dans son propre processus
- **Logs séparés** : Chaque bot a son propre fichier de log
- **Gestion sécurisée des tokens** : Les tokens sont stockés de manière sécurisée

## 📁 Structure des Fichiers

```
parfume/
├── master_bot.py          # Bot principal ✅
├── bot_manager.py         # Gestionnaire des bots ✅
├── api_client.py          # Client APIs ✅
├── config.py              # Configuration ✅
├── start.py               # Script de démarrage ✅
├── .env                   # Configuration secrète ✅
├── bots/                  # Bots générés automatiquement
├── logs/                  # Fichiers de logs
│   ├── master_bot.log     # Logs du bot maître
│   └── bot_*.log          # Logs des bots secondaires
└── bots_data.json         # Base de données des bots
```

## 🚨 Dépannage

### Le bot ne répond pas
1. Vérifiez que le processus est en cours d'exécution
2. Consultez les logs : `logs/master_bot.log`
3. Vérifiez votre connexion internet

### Erreur "Accès refusé"
- Assurez-vous d'utiliser le bon compte Telegram (ID: 7763623565)

### Bot secondaire ne démarre pas
1. Vérifiez le token du bot dans `/bot_info <bot_id>`
2. Consultez les logs du bot spécifique
3. Vérifiez que le bot n'est pas déjà en cours d'exécution

### APIs ne répondent pas
1. Testez avec `/test_*` commands
2. Vérifiez votre connexion internet
3. Les APIs externes peuvent être temporairement indisponibles

## 📞 Support

- **Logs du système** : Consultez `logs/master_bot.log`
- **Logs des bots** : Consultez `logs/bot_<id>.log`
- **Configuration** : Vérifiez le fichier `.env`

## 🎯 Conseils d'Utilisation

1. **Nommage des bots** : Utilisez des noms descriptifs
2. **Gestion des tokens** : Gardez vos tokens secrets
3. **Surveillance** : Vérifiez régulièrement le statut avec `/list_bots`
4. **Nettoyage** : Supprimez les bots inutilisés avec `/delete_bot`
5. **Tests** : Utilisez les commandes `/test_*` pour vérifier les APIs

---

🤖 **Votre système de bots est maintenant opérationnel !**

Commencez par envoyer `/start` au bot @dev_souhail_bot sur Telegram.
