import requests
import logging
from config import API_BASE_URLS, API_KEY, DEFAULT_REGION

logger = logging.getLogger(__name__)

class APIClient:
    """Client pour interagir avec les APIs externes"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = 30
    
    def check_status(self, uid):
        """Vérifier le statut d'un utilisateur"""
        try:
            url = f"{API_BASE_URLS['check_status']}?key={API_KEY}&uid={uid}"
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Erreur lors de la vérification du statut pour UID {uid}: {e}")
            return None
    
    def spam_user(self, uid):
        """Envoyer un spam à un utilisateur"""
        try:
            url = f"{API_BASE_URLS['spam']}?id={uid}"
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Erreur lors de l'envoi de spam pour UID {uid}: {e}")
            return None
    
    def get_events(self, region=DEFAULT_REGION):
        """Récupérer les événements pour une région"""
        try:
            url = f"{API_BASE_URLS['events']}?region={region}&key={API_KEY}"
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Erreur lors de la récupération des événements pour la région {region}: {e}")
            return None
    
    def get_info(self):
        """Récupérer les informations générales"""
        try:
            url = API_BASE_URLS['info']
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Erreur lors de la récupération des informations: {e}")
            return None
