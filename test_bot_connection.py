#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de connexion du bot
"""

import telebot
from config import MASTER_BOT_TOKEN

def test_bot_connection():
    """Tester la connexion du bot"""
    print("🔍 Test de connexion du bot...")
    
    try:
        bot = telebot.TeleBot(MASTER_BOT_TOKEN)
        
        # Tester la connexion
        me = bot.get_me()
        print(f"✅ Bot connecté avec succès!")
        print(f"📝 Nom: {me.first_name}")
        print(f"🆔 Username: @{me.username}")
        print(f"🔢 ID: {me.id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return False

if __name__ == '__main__':
    test_bot_connection()
