# Bot Créateur de Bots 🤖

Un système de bot Telegram qui permet de créer et gérer automatiquement d'autres bots Telegram avec des fonctionnalités personnalisées.

## 🚀 Fonctionnalités

### Bot Maître
- **Création de bots** : Créer automatiquement de nouveaux bots Telegram
- **Gestion des bots** : <PERSON><PERSON><PERSON><PERSON>, arr<PERSON><PERSON>, supprimer des bots
- **Surveillance** : Monitorer le statut de tous les bots créés
- **APIs intégrées** : Accès aux APIs externes pour les fonctionnalités

### Bots Secondaires
Chaque bot créé peut avoir les fonctionnalités suivantes :
- **Check Status** : Vérifier le statut d'un utilisateur via API
- **Spam** : Envoyer des messages via API
- **Events** : Récupérer les événements par région
- **Info** : Obtenir des informations générales

## 📋 APIs Intégrées

1. **Check Status API** : `https://ch9ayfa-check-1.vercel.app/check_status?key=ch9ayfa&uid={uid}`
2. **Spam API** : `https://spam-ch9ayfa.vercel.app/spam?id={uid}`
3. **Events API** : `https://eventes-ch9ayfa.vercel.app/eventes?region=ME&key=ch9ayfa`
4. **Info API** : `https://info-ch9ayfa.vercel.app/2511293320`

## 🛠️ Installation

### Prérequis
- Python 3.7+
- pip

### Étapes d'installation

1. **Cloner le projet**
```bash
git clone <repository-url>
cd parfume
```

2. **Installer les dépendances**
```bash
pip install -r requirements.txt
```

3. **Configuration**
```bash
cp .env.example .env
```

Éditer le fichier `.env` avec vos informations :
```env
MASTER_BOT_TOKEN=your_master_bot_token_here
ADMIN_USER_ID=your_admin_user_id_here
```

4. **Créer les dossiers nécessaires**
```bash
mkdir logs bots
```

## 🚀 Utilisation

### Démarrer le Bot Maître
```bash
python master_bot.py
```

### Commandes du Bot Maître

#### Gestion des Bots
- `/start` - Afficher le menu principal
- `/create_bot <nom> <token> [description]` - Créer un nouveau bot
- `/list_bots` - Lister tous les bots créés
- `/start_bot <bot_id>` - Démarrer un bot spécifique
- `/stop_bot <bot_id>` - Arrêter un bot spécifique
- `/delete_bot <bot_id>` - Supprimer un bot
- `/bot_info <bot_id>` - Informations détaillées sur un bot

#### Test des APIs
- `/test_check <uid>` - Tester l'API check_status
- `/test_spam <uid>` - Tester l'API spam
- `/test_events [region]` - Tester l'API events (région par défaut: ME)
- `/test_info` - Tester l'API info

### Exemple d'utilisation

1. **Créer un bot**
```
/create_bot MonBot 1234567890:ABCdefGHIjklMNOpqrsTUVwxyz "Bot de test"
```

2. **Démarrer le bot**
```
/start_bot <bot_id_retourné>
```

3. **Vérifier le statut**
```
/list_bots
```

## 📁 Structure du Projet

```
parfume/
├── master_bot.py          # Bot principal
├── bot_manager.py         # Gestionnaire des bots
├── api_client.py          # Client pour les APIs externes
├── config.py              # Configuration
├── requirements.txt       # Dépendances Python
├── .env.example          # Exemple de configuration
├── README.md             # Documentation
├── bots/                 # Dossier des bots générés
├── logs/                 # Fichiers de logs
└── bots_data.json        # Base de données des bots
```

## 🔧 Configuration Avancée

### Variables d'environnement
- `MASTER_BOT_TOKEN` : Token du bot maître Telegram
- `ADMIN_USER_ID` : ID Telegram de l'administrateur

### Paramètres dans config.py
- `MAX_BOTS` : Nombre maximum de bots (défaut: 10)
- `DEFAULT_REGION` : Région par défaut pour les événements (défaut: ME)
- `LOG_LEVEL` : Niveau de logging (défaut: INFO)

## 🔒 Sécurité

- Seul l'administrateur configuré peut utiliser le bot maître
- Chaque bot secondaire est isolé dans son propre processus
- Les logs sont séparés pour chaque bot
- Les tokens sont stockés de manière sécurisée

## 🐛 Dépannage

### Problèmes courants

1. **Bot ne démarre pas**
   - Vérifier le token dans `.env`
   - Vérifier les permissions du bot Telegram

2. **APIs ne répondent pas**
   - Vérifier la connectivité internet
   - Vérifier les URLs des APIs dans `config.py`

3. **Erreur de permissions**
   - Vérifier l'ADMIN_USER_ID dans `.env`
   - S'assurer d'utiliser le bon ID utilisateur Telegram

### Logs
Les logs sont disponibles dans :
- `logs/master_bot.log` : Logs du bot maître
- `logs/bot_<id>.log` : Logs de chaque bot secondaire

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.

## 🤝 Contribution

Les contributions sont les bienvenues ! N'hésitez pas à ouvrir une issue ou une pull request.

## 📞 Support

Pour toute question ou problème, veuillez ouvrir une issue sur GitHub.
