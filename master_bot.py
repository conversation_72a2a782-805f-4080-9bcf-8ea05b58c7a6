#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bot Maître - Créateur et gestionnaire de bots
"""

import telebot
import logging
import os
from datetime import datetime
from config import MASTER_BOT_TOKEN, ADMIN_USER_ID, LOG_FORMAT, LOG_LEVEL, LOGS_DIR
from bot_manager import BotManager
from api_client import APIClient

# Configuration du logging
os.makedirs(LOGS_DIR, exist_ok=True)
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT,
    handlers=[
        logging.FileHandler(f'{LOGS_DIR}/master_bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Vérification de la configuration
if not MASTER_BOT_TOKEN:
    logger.error("MASTER_BOT_TOKEN non configuré dans le fichier .env")
    exit(1)

if not ADMIN_USER_ID:
    logger.error("ADMIN_USER_ID non configuré dans le fichier .env")
    exit(1)

# Initialisation
bot = telebot.TeleBot(MASTER_BOT_TOKEN)
bot_manager = BotManager()
api_client = APIClient()

def is_admin(user_id):
    """Vérifier si l'utilisateur est admin"""
    return str(user_id) == str(ADMIN_USER_ID)

@bot.message_handler(commands=['start'])
def start_command(message):
    """Commande de démarrage"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé. Seul l'administrateur peut utiliser ce bot.")
        return
    
    welcome_text = """
🤖 **Bot Maître - Gestionnaire de Bots**

📋 **Commandes disponibles:**

**Gestion des bots:**
• `/create_bot <nom> <token> [description]` - Créer un nouveau bot
• `/list_bots` - Lister tous les bots
• `/start_bot <bot_id>` - Démarrer un bot
• `/stop_bot <bot_id>` - Arrêter un bot
• `/delete_bot <bot_id>` - Supprimer un bot
• `/bot_info <bot_id>` - Informations sur un bot

**APIs de test:**
• `/test_check <uid>` - Tester l'API check_status
• `/test_spam <uid>` - Tester l'API spam
• `/test_events [region]` - Tester l'API events
• `/test_info` - Tester l'API info

• `/help` - Afficher cette aide
"""
    
    bot.reply_to(message, welcome_text, parse_mode='Markdown')

@bot.message_handler(commands=['help'])
def help_command(message):
    """Commande d'aide"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    start_command(message)

@bot.message_handler(commands=['create_bot'])
def create_bot_command(message):
    """Créer un nouveau bot"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    try:
        args = message.text.split(maxsplit=3)
        if len(args) < 3:
            bot.reply_to(message, "❌ Usage: `/create_bot <nom> <token> [description]`", parse_mode='Markdown')
            return
        
        name = args[1]
        token = args[2]
        description = args[3] if len(args) > 3 else ""
        
        bot_id, result_message = bot_manager.create_bot(name, token, description)
        
        if bot_id:
            response = f"✅ **Bot créé avec succès !**\n\n"
            response += f"🆔 **ID:** `{bot_id}`\n"
            response += f"📝 **Nom:** {name}\n"
            response += f"📄 **Description:** {description}\n\n"
            response += f"Pour démarrer le bot: `/start_bot {bot_id}`"
        else:
            response = f"❌ **Erreur:** {result_message}"
        
        bot.reply_to(message, response, parse_mode='Markdown')
        
    except Exception as e:
        logger.error(f"Erreur dans create_bot_command: {e}")
        bot.reply_to(message, f"❌ Erreur lors de la création du bot: {e}")

@bot.message_handler(commands=['list_bots'])
def list_bots_command(message):
    """Lister tous les bots"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    try:
        bots = bot_manager.list_bots()
        
        if not bots:
            bot.reply_to(message, "📭 Aucun bot créé pour le moment.")
            return
        
        response = f"🤖 **Liste des bots ({len(bots)}):**\n\n"
        
        for bot_id, bot_data in bots.items():
            status_emoji = "🟢" if bot_data['status'] == 'running' else "🔴"
            response += f"{status_emoji} **{bot_data['name']}**\n"
            response += f"   🆔 ID: `{bot_id[:8]}...`\n"
            response += f"   📊 Statut: {bot_data['status']}\n"
            response += f"   📅 Créé: {bot_data['created_at'][:10]}\n\n"
        
        bot.reply_to(message, response, parse_mode='Markdown')
        
    except Exception as e:
        logger.error(f"Erreur dans list_bots_command: {e}")
        bot.reply_to(message, f"❌ Erreur lors de la récupération de la liste: {e}")

@bot.message_handler(commands=['start_bot'])
def start_bot_command(message):
    """Démarrer un bot"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    try:
        args = message.text.split()
        if len(args) < 2:
            bot.reply_to(message, "❌ Usage: `/start_bot <bot_id>`", parse_mode='Markdown')
            return
        
        bot_id = args[1]
        success, result_message = bot_manager.start_bot(bot_id)
        
        if success:
            bot_info = bot_manager.get_bot_info(bot_id)
            response = f"✅ **Bot démarré !**\n\n"
            response += f"🤖 **Nom:** {bot_info['name']}\n"
            response += f"🆔 **ID:** `{bot_id[:8]}...`\n"
            response += f"📊 **Statut:** En cours d'exécution\n"
            response += f"ℹ️ {result_message}"
        else:
            response = f"❌ **Erreur:** {result_message}"
        
        bot.reply_to(message, response, parse_mode='Markdown')
        
    except Exception as e:
        logger.error(f"Erreur dans start_bot_command: {e}")
        bot.reply_to(message, f"❌ Erreur lors du démarrage du bot: {e}")

@bot.message_handler(commands=['stop_bot'])
def stop_bot_command(message):
    """Arrêter un bot"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    try:
        args = message.text.split()
        if len(args) < 2:
            bot.reply_to(message, "❌ Usage: `/stop_bot <bot_id>`", parse_mode='Markdown')
            return
        
        bot_id = args[1]
        success, result_message = bot_manager.stop_bot(bot_id)
        
        if success:
            bot_info = bot_manager.get_bot_info(bot_id)
            response = f"🛑 **Bot arrêté !**\n\n"
            response += f"🤖 **Nom:** {bot_info['name']}\n"
            response += f"🆔 **ID:** `{bot_id[:8]}...`\n"
            response += f"📊 **Statut:** Arrêté"
        else:
            response = f"❌ **Erreur:** {result_message}"
        
        bot.reply_to(message, response, parse_mode='Markdown')
        
    except Exception as e:
        logger.error(f"Erreur dans stop_bot_command: {e}")
        bot.reply_to(message, f"❌ Erreur lors de l'arrêt du bot: {e}")

@bot.message_handler(commands=['delete_bot'])
def delete_bot_command(message):
    """Supprimer un bot"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    try:
        args = message.text.split()
        if len(args) < 2:
            bot.reply_to(message, "❌ Usage: `/delete_bot <bot_id>`", parse_mode='Markdown')
            return
        
        bot_id = args[1]
        bot_info = bot_manager.get_bot_info(bot_id)
        
        if not bot_info:
            bot.reply_to(message, "❌ Bot non trouvé.")
            return
        
        success, result_message = bot_manager.delete_bot(bot_id)
        
        if success:
            response = f"🗑️ **Bot supprimé !**\n\n"
            response += f"🤖 **Nom:** {bot_info['name']}\n"
            response += f"🆔 **ID:** `{bot_id[:8]}...`"
        else:
            response = f"❌ **Erreur:** {result_message}"
        
        bot.reply_to(message, response, parse_mode='Markdown')
        
    except Exception as e:
        logger.error(f"Erreur dans delete_bot_command: {e}")
        bot.reply_to(message, f"❌ Erreur lors de la suppression du bot: {e}")

@bot.message_handler(commands=['bot_info'])
def bot_info_command(message):
    """Informations détaillées sur un bot"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    try:
        args = message.text.split()
        if len(args) < 2:
            bot.reply_to(message, "❌ Usage: `/bot_info <bot_id>`", parse_mode='Markdown')
            return
        
        bot_id = args[1]
        bot_info = bot_manager.get_bot_info(bot_id)
        
        if not bot_info:
            bot.reply_to(message, "❌ Bot non trouvé.")
            return
        
        status_emoji = "🟢" if bot_info['status'] == 'running' else "🔴"
        
        response = f"🤖 **Informations du Bot**\n\n"
        response += f"📝 **Nom:** {bot_info['name']}\n"
        response += f"🆔 **ID:** `{bot_id}`\n"
        response += f"{status_emoji} **Statut:** {bot_info['status']}\n"
        response += f"📄 **Description:** {bot_info['description']}\n"
        response += f"📅 **Créé le:** {bot_info['created_at']}\n"
        response += f"🔧 **Fonctionnalités:** {', '.join(bot_info['features'])}\n"
        
        if bot_info['process_id']:
            response += f"🔢 **PID:** {bot_info['process_id']}\n"
        
        response += f"📋 **Log:** `{bot_info['log_file']}`"
        
        bot.reply_to(message, response, parse_mode='Markdown')
        
    except Exception as e:
        logger.error(f"Erreur dans bot_info_command: {e}")
        bot.reply_to(message, f"❌ Erreur lors de la récupération des informations: {e}")

# Commandes de test des APIs
@bot.message_handler(commands=['test_check'])
def test_check_command(message):
    """Tester l'API check_status"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    try:
        args = message.text.split()
        if len(args) < 2:
            bot.reply_to(message, "❌ Usage: `/test_check <uid>`", parse_mode='Markdown')
            return
        
        uid = args[1]
        result = api_client.check_status(uid)
        
        if result:
            response = f"✅ **Test API check_status réussi**\n\n"
            response += f"🆔 **UID:** {uid}\n"
            response += f"📊 **Résultat:**\n```json\n{result}```"
        else:
            response = f"❌ **Test API check_status échoué**\n\nImpossible de récupérer le statut pour UID {uid}"
        
        bot.reply_to(message, response, parse_mode='Markdown')
        
    except Exception as e:
        logger.error(f"Erreur dans test_check_command: {e}")
        bot.reply_to(message, f"❌ Erreur lors du test: {e}")

@bot.message_handler(commands=['test_spam'])
def test_spam_command(message):
    """Tester l'API spam"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    try:
        args = message.text.split()
        if len(args) < 2:
            bot.reply_to(message, "❌ Usage: `/test_spam <uid>`", parse_mode='Markdown')
            return
        
        uid = args[1]
        result = api_client.spam_user(uid)
        
        if result:
            response = f"✅ **Test API spam réussi**\n\n"
            response += f"🆔 **UID:** {uid}\n"
            response += f"📊 **Résultat:**\n```json\n{result}```"
        else:
            response = f"❌ **Test API spam échoué**\n\nImpossible d'envoyer le spam à UID {uid}"
        
        bot.reply_to(message, response, parse_mode='Markdown')
        
    except Exception as e:
        logger.error(f"Erreur dans test_spam_command: {e}")
        bot.reply_to(message, f"❌ Erreur lors du test: {e}")

@bot.message_handler(commands=['test_events'])
def test_events_command(message):
    """Tester l'API events"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    try:
        args = message.text.split()
        region = args[1] if len(args) > 1 else 'ME'
        
        result = api_client.get_events(region)
        
        if result:
            response = f"✅ **Test API events réussi**\n\n"
            response += f"🌍 **Région:** {region}\n"
            response += f"📊 **Résultat:**\n```json\n{result}```"
        else:
            response = f"❌ **Test API events échoué**\n\nImpossible de récupérer les événements pour la région {region}"
        
        bot.reply_to(message, response, parse_mode='Markdown')
        
    except Exception as e:
        logger.error(f"Erreur dans test_events_command: {e}")
        bot.reply_to(message, f"❌ Erreur lors du test: {e}")

@bot.message_handler(commands=['test_info'])
def test_info_command(message):
    """Tester l'API info"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    try:
        result = api_client.get_info()
        
        if result:
            response = f"✅ **Test API info réussi**\n\n"
            response += f"📊 **Résultat:**\n```json\n{result}```"
        else:
            response = f"❌ **Test API info échoué**\n\nImpossible de récupérer les informations"
        
        bot.reply_to(message, response, parse_mode='Markdown')
        
    except Exception as e:
        logger.error(f"Erreur dans test_info_command: {e}")
        bot.reply_to(message, f"❌ Erreur lors du test: {e}")

if __name__ == '__main__':
    logger.info("Démarrage du Bot Maître")
    logger.info(f"Admin User ID: {ADMIN_USER_ID}")
    
    try:
        bot.infinity_polling()
    except Exception as e:
        logger.error(f"Erreur fatale: {e}")
        raise
