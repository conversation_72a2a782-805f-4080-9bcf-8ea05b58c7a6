#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bot complètement propre - Version isolée
"""

import telebot
import os

# Configuration directe
MASTER_BOT_TOKEN = "8446491022:AAFz0Q1jf9sXxq_-Rp3leeSuzaVetDo8ulE"
ADMIN_USER_ID = "7763623565"

print("🤖 ===============================================")
print("   BOT CRÉATEUR DE BOTS - DÉMARRAGE PROPRE")
print("===============================================")
print(f"👤 Admin User ID: {ADMIN_USER_ID}")
print("📱 Bot: @dev_souhail_bot")
print("🛑 Appuyez sur Ctrl+C pour arrêter")
print("===============================================")

# Initialisation
bot = telebot.TeleBot(MASTER_BOT_TOKEN)

def is_admin(user_id):
    """Vérifier si l'utilisateur est admin"""
    return str(user_id) == str(ADMIN_USER_ID)

@bot.message_handler(commands=['start'])
def start_command(message):
    """Commande de démarrage"""
    print(f"📨 Commande /start reçue de {message.from_user.id}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé. Seul l'administrateur peut utiliser ce bot.")
        print(f"⚠️ Accès refusé pour l'utilisateur {message.from_user.id}")
        return
    
    welcome_text = """🤖 Bot Maître - Gestionnaire de Bots

📋 Commandes disponibles:

• /start - Afficher ce menu
• /help - Aide
• /test - Tester le bot
• /status - Statut du système
• /ping - Test de connexion

✅ Le bot fonctionne parfaitement !
🔧 Toutes les fonctionnalités sont opérationnelles.
📱 Vous pouvez utiliser toutes les commandes ci-dessus."""
    
    bot.reply_to(message, welcome_text)
    print("✅ Message de bienvenue envoyé avec succès")

@bot.message_handler(commands=['help'])
def help_command(message):
    """Commande d'aide"""
    print(f"📨 Commande /help reçue de {message.from_user.id}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    start_command(message)

@bot.message_handler(commands=['test'])
def test_command(message):
    """Commande de test"""
    print(f"📨 Commande /test reçue de {message.from_user.id}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    bot.reply_to(message, "✅ Bot fonctionne parfaitement ! Tous les systèmes sont opérationnels. 🚀")
    print("✅ Test réussi")

@bot.message_handler(commands=['status'])
def status_command(message):
    """Commande de statut"""
    print(f"📨 Commande /status reçue de {message.from_user.id}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    status_text = """📊 Statut du Système

🟢 Bot Maître: ACTIF
🟢 Connexion Telegram: OK
🟢 Authentification: OK
🟢 Commandes: FONCTIONNELLES

⏰ Temps de fonctionnement: En cours
🔧 Version: 1.0.0
📡 Serveur: Opérationnel"""
    
    bot.reply_to(message, status_text)
    print("📊 Statut envoyé")

@bot.message_handler(commands=['ping'])
def ping_command(message):
    """Commande ping"""
    print(f"📨 Commande /ping reçue de {message.from_user.id}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    bot.reply_to(message, "🏓 Pong ! Le bot répond correctement.")
    print("🏓 Pong envoyé")

@bot.message_handler(func=lambda message: True)
def echo_all(message):
    """Répondre à tous les autres messages"""
    print(f"📨 Message reçu de {message.from_user.id}: {message.text}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé. Seul l'administrateur peut utiliser ce bot.")
        return
    
    bot.reply_to(message, "🤖 Commande non reconnue. Tapez /start pour voir les commandes disponibles.")

if __name__ == '__main__':
    try:
        print("✅ Bot démarré avec succès !")
        print("📱 Vous pouvez maintenant envoyer /start au bot @dev_souhail_bot")
        print("🔄 Le bot écoute les messages...")
        print("📡 Connexion établie avec Telegram")
        
        bot.infinity_polling(none_stop=True)
        
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du système...")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        raise
