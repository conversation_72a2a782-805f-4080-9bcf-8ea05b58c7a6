#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bot complètement propre - Version isolée
"""

import telebot
import os
import sys
import logging

# Ajouter le répertoire courant au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configuration directe
MASTER_BOT_TOKEN = "8446491022:AAFz0Q1jf9sXxq_-Rp3leeSuzaVetDo8ulE"
ADMIN_USER_ID = "7763623565"

# Importer nos modules
try:
    from bot_manager import BotManager
    from api_client import APIClient
    bot_manager = BotManager()
    api_client = APIClient()
    print("✅ Modules de gestion des bots chargés")
except ImportError as e:
    print(f"⚠️ Modules de gestion non disponibles: {e}")
    bot_manager = None
    api_client = None

print("🤖 ===============================================")
print("   BOT CRÉATEUR DE BOTS - DÉMARRAGE PROPRE")
print("===============================================")
print(f"👤 Admin User ID: {ADMIN_USER_ID}")
print("📱 Bot: @dev_souhail_bot")
print("🛑 Appuyez sur Ctrl+C pour arrêter")
print("===============================================")

# Initialisation
bot = telebot.TeleBot(MASTER_BOT_TOKEN)

def is_admin(user_id):
    """Vérifier si l'utilisateur est admin"""
    return str(user_id) == str(ADMIN_USER_ID)

@bot.message_handler(commands=['start'])
def start_command(message):
    """Commande de démarrage"""
    print(f"📨 Commande /start reçue de {message.from_user.id}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé. Seul l'administrateur peut utiliser ce bot.")
        print(f"⚠️ Accès refusé pour l'utilisateur {message.from_user.id}")
        return
    
    welcome_text = """🤖 Bot Maître - Gestionnaire de Bots

📋 Commandes disponibles:

🤖 GESTION DES BOTS:
• /create_bot <nom> <token> [description] - Créer un nouveau bot
• /list_bots - Lister tous les bots
• /start_bot <bot_id> - Démarrer un bot
• /stop_bot <bot_id> - Arrêter un bot

🔧 SYSTÈME:
• /start - Afficher ce menu
• /help - Aide
• /test - Tester le bot
• /status - Statut du système
• /ping - Test de connexion

✅ Le bot fonctionne parfaitement !
🚀 Vous pouvez créer et gérer des bots automatiquement."""
    
    bot.reply_to(message, welcome_text)
    print("✅ Message de bienvenue envoyé avec succès")

@bot.message_handler(commands=['help'])
def help_command(message):
    """Commande d'aide"""
    print(f"📨 Commande /help reçue de {message.from_user.id}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    start_command(message)

@bot.message_handler(commands=['test'])
def test_command(message):
    """Commande de test"""
    print(f"📨 Commande /test reçue de {message.from_user.id}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    bot.reply_to(message, "✅ Bot fonctionne parfaitement ! Tous les systèmes sont opérationnels. 🚀")
    print("✅ Test réussi")

@bot.message_handler(commands=['status'])
def status_command(message):
    """Commande de statut"""
    print(f"📨 Commande /status reçue de {message.from_user.id}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return
    
    status_text = """📊 Statut du Système

🟢 Bot Maître: ACTIF
🟢 Connexion Telegram: OK
🟢 Authentification: OK
🟢 Commandes: FONCTIONNELLES

⏰ Temps de fonctionnement: En cours
🔧 Version: 1.0.0
📡 Serveur: Opérationnel"""
    
    bot.reply_to(message, status_text)
    print("📊 Statut envoyé")

@bot.message_handler(commands=['ping'])
def ping_command(message):
    """Commande ping"""
    print(f"📨 Commande /ping reçue de {message.from_user.id}")

    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return

    bot.reply_to(message, "🏓 Pong ! Le bot répond correctement.")
    print("🏓 Pong envoyé")

@bot.message_handler(commands=['create_bot'])
def create_bot_command(message):
    """Créer un nouveau bot"""
    print(f"📨 Commande /create_bot reçue de {message.from_user.id}")

    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return

    if not bot_manager:
        bot.reply_to(message, "❌ Système de gestion des bots non disponible.")
        return

    try:
        args = message.text.split(maxsplit=3)
        if len(args) < 3:
            usage_text = """❌ Usage incorrect !

Format: /create_bot <nom> <token> [description]

Exemple:
/create_bot MonBot 1234567890:ABC-DEF "Description du bot"

Pour obtenir un token:
1. Contactez @BotFather sur Telegram
2. Tapez /newbot
3. Suivez les instructions
4. Copiez le token reçu"""

            bot.reply_to(message, usage_text)
            return

        name = args[1]
        token = args[2]
        description = args[3] if len(args) > 3 else ""

        print(f"🔧 Création du bot: {name}")
        bot_id, result_message = bot_manager.create_bot(name, token, description)

        if bot_id:
            response = f"""✅ Bot créé avec succès !

🆔 ID: {bot_id[:8]}...
📝 Nom: {name}
📄 Description: {description}

Pour démarrer le bot: /start_bot {bot_id}
Pour voir tous les bots: /list_bots"""
            print(f"✅ Bot créé: {name} (ID: {bot_id[:8]}...)")
        else:
            response = f"❌ Erreur: {result_message}"
            print(f"❌ Erreur création: {result_message}")

        bot.reply_to(message, response)

    except Exception as e:
        print(f"❌ Erreur dans create_bot_command: {e}")
        bot.reply_to(message, f"❌ Erreur lors de la création du bot: {e}")

@bot.message_handler(commands=['list_bots'])
def list_bots_command(message):
    """Lister tous les bots"""
    print(f"📨 Commande /list_bots reçue de {message.from_user.id}")

    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return

    if not bot_manager:
        bot.reply_to(message, "❌ Système de gestion des bots non disponible.")
        return

    try:
        bots = bot_manager.list_bots()

        if not bots:
            bot.reply_to(message, "📭 Aucun bot créé pour le moment.\n\nUtilisez /create_bot pour en créer un.")
            return

        response = f"🤖 Liste des bots ({len(bots)}):\n\n"

        for bot_id, bot_data in bots.items():
            status_emoji = "🟢" if bot_data['status'] == 'running' else "🔴"
            response += f"{status_emoji} {bot_data['name']}\n"
            response += f"   🆔 ID: {bot_id[:8]}...\n"
            response += f"   📊 Statut: {bot_data['status']}\n"
            response += f"   📅 Créé: {bot_data['created_at'][:10]}\n\n"

        bot.reply_to(message, response)
        print(f"📋 Liste envoyée: {len(bots)} bots")

    except Exception as e:
        print(f"❌ Erreur dans list_bots_command: {e}")
        bot.reply_to(message, f"❌ Erreur lors de la récupération de la liste: {e}")

@bot.message_handler(commands=['start_bot'])
def start_bot_command(message):
    """Démarrer un bot"""
    print(f"📨 Commande /start_bot reçue de {message.from_user.id}")

    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé.")
        return

    if not bot_manager:
        bot.reply_to(message, "❌ Système de gestion des bots non disponible.")
        return

    try:
        args = message.text.split()
        if len(args) < 2:
            bot.reply_to(message, "❌ Usage: /start_bot <bot_id>\n\nUtilisez /list_bots pour voir les IDs disponibles.")
            return

        bot_id = args[1]
        success, result_message = bot_manager.start_bot(bot_id)

        if success:
            response = f"✅ Bot démarré avec succès !\n\n{result_message}"
            print(f"✅ Bot démarré: {bot_id[:8]}...")
        else:
            response = f"❌ Erreur: {result_message}"
            print(f"❌ Erreur démarrage: {result_message}")

        bot.reply_to(message, response)

    except Exception as e:
        print(f"❌ Erreur dans start_bot_command: {e}")
        bot.reply_to(message, f"❌ Erreur lors du démarrage: {e}")

@bot.message_handler(func=lambda message: True)
def echo_all(message):
    """Répondre à tous les autres messages"""
    print(f"📨 Message reçu de {message.from_user.id}: {message.text}")
    
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ Accès refusé. Seul l'administrateur peut utiliser ce bot.")
        return
    
    bot.reply_to(message, "🤖 Commande non reconnue. Tapez /start pour voir les commandes disponibles.")

if __name__ == '__main__':
    try:
        print("✅ Bot démarré avec succès !")
        print("📱 Vous pouvez maintenant envoyer /start au bot @dev_souhail_bot")
        print("🔄 Le bot écoute les messages...")
        print("📡 Connexion établie avec Telegram")
        
        bot.infinity_polling(none_stop=True)
        
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du système...")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        raise
