#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour le système de bots
"""

import unittest
import json
import os
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from bot_manager import Bot<PERSON>anager
from api_client import APIClient

class TestBotManager(unittest.TestCase):
    """Tests pour le gestionnaire de bots"""
    
    def setUp(self):
        """Configuration avant chaque test"""
        self.test_dir = tempfile.mkdtemp()
        self.original_bot_data_file = 'bots_data.json'
        self.test_bot_data_file = os.path.join(self.test_dir, 'test_bots_data.json')
        
        # Patcher le fichier de données
        patcher = patch('bot_manager.BOT_DATA_FILE', self.test_bot_data_file)
        patcher.start()
        self.addCleanup(patcher.stop)
        
        # Patcher le dossier des bots
        self.test_bots_dir = os.path.join(self.test_dir, 'bots')
        os.makedirs(self.test_bots_dir, exist_ok=True)
        patcher_bots = patch('bot_manager.BotManager.create_bot_file')
        self.mock_create_bot_file = patcher_bots.start()
        self.addCleanup(patcher_bots.stop)
        
        self.bot_manager = BotManager()
    
    def tearDown(self):
        """Nettoyage après chaque test"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_create_bot_success(self):
        """Test de création de bot réussie"""
        bot_id, message = self.bot_manager.create_bot(
            name="TestBot",
            token="123456:ABC-DEF",
            description="Bot de test"
        )
        
        self.assertIsNotNone(bot_id)
        self.assertEqual(message, "Bot créé avec succès")
        self.assertIn(bot_id, self.bot_manager.bots)
        
        bot_data = self.bot_manager.bots[bot_id]
        self.assertEqual(bot_data['name'], "TestBot")
        self.assertEqual(bot_data['token'], "123456:ABC-DEF")
        self.assertEqual(bot_data['description'], "Bot de test")
        self.assertEqual(bot_data['status'], 'stopped')
    
    def test_create_bot_max_limit(self):
        """Test de la limite maximale de bots"""
        # Créer le nombre maximum de bots
        with patch('bot_manager.MAX_BOTS', 2):
            bot_manager = BotManager()
            
            # Créer 2 bots (limite)
            bot_id1, _ = bot_manager.create_bot("Bot1", "token1")
            bot_id2, _ = bot_manager.create_bot("Bot2", "token2")
            
            self.assertIsNotNone(bot_id1)
            self.assertIsNotNone(bot_id2)
            
            # Tenter de créer un 3ème bot (doit échouer)
            bot_id3, message = bot_manager.create_bot("Bot3", "token3")
            
            self.assertIsNone(bot_id3)
            self.assertEqual(message, "Nombre maximum de bots atteint")
    
    def test_list_bots(self):
        """Test de listage des bots"""
        # Créer quelques bots
        bot_id1, _ = self.bot_manager.create_bot("Bot1", "token1")
        bot_id2, _ = self.bot_manager.create_bot("Bot2", "token2")
        
        bots = self.bot_manager.list_bots()
        
        self.assertEqual(len(bots), 2)
        self.assertIn(bot_id1, bots)
        self.assertIn(bot_id2, bots)
    
    def test_get_bot_info(self):
        """Test de récupération d'informations de bot"""
        bot_id, _ = self.bot_manager.create_bot("TestBot", "token123")
        
        bot_info = self.bot_manager.get_bot_info(bot_id)
        
        self.assertIsNotNone(bot_info)
        self.assertEqual(bot_info['name'], "TestBot")
        self.assertEqual(bot_info['token'], "token123")
        
        # Test avec un ID inexistant
        fake_info = self.bot_manager.get_bot_info("fake-id")
        self.assertIsNone(fake_info)
    
    @patch('bot_manager.subprocess.Popen')
    def test_start_bot(self, mock_popen):
        """Test de démarrage de bot"""
        # Configurer le mock
        mock_process = Mock()
        mock_process.pid = 12345
        mock_popen.return_value = mock_process
        
        # Créer un bot
        bot_id, _ = self.bot_manager.create_bot("TestBot", "token123")
        
        # Démarrer le bot
        success, message = self.bot_manager.start_bot(bot_id)
        
        self.assertTrue(success)
        self.assertIn("12345", message)
        
        bot_data = self.bot_manager.bots[bot_id]
        self.assertEqual(bot_data['status'], 'running')
        self.assertEqual(bot_data['process_id'], 12345)
    
    def test_start_nonexistent_bot(self):
        """Test de démarrage d'un bot inexistant"""
        success, message = self.bot_manager.start_bot("fake-id")
        
        self.assertFalse(success)
        self.assertEqual(message, "Bot non trouvé")
    
    @patch('bot_manager.os.kill')
    def test_stop_bot(self, mock_kill):
        """Test d'arrêt de bot"""
        # Créer et "démarrer" un bot
        bot_id, _ = self.bot_manager.create_bot("TestBot", "token123")
        self.bot_manager.bots[bot_id]['status'] = 'running'
        self.bot_manager.bots[bot_id]['process_id'] = 12345
        
        # Arrêter le bot
        success, message = self.bot_manager.stop_bot(bot_id)
        
        self.assertTrue(success)
        self.assertEqual(message, "Bot arrêté avec succès")
        
        bot_data = self.bot_manager.bots[bot_id]
        self.assertEqual(bot_data['status'], 'stopped')
        self.assertIsNone(bot_data['process_id'])
        
        # Vérifier que os.kill a été appelé
        mock_kill.assert_called_once_with(12345, 9)

class TestAPIClient(unittest.TestCase):
    """Tests pour le client API"""
    
    def setUp(self):
        """Configuration avant chaque test"""
        self.api_client = APIClient()
    
    @patch('api_client.requests.Session.get')
    def test_check_status_success(self, mock_get):
        """Test de vérification de statut réussie"""
        # Configurer le mock
        mock_response = Mock()
        mock_response.json.return_value = {"status": "active", "uid": "123"}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = self.api_client.check_status("123")
        
        self.assertIsNotNone(result)
        self.assertEqual(result["status"], "active")
        self.assertEqual(result["uid"], "123")
    
    @patch('requests.Session.get')
    def test_check_status_failure(self, mock_get):
        """Test de vérification de statut échouée"""
        # Configurer le mock pour lever une exception
        mock_get.side_effect = Exception("Network error")

        result = self.api_client.check_status("123")

        self.assertIsNone(result)
    
    @patch('api_client.requests.Session.get')
    def test_spam_user_success(self, mock_get):
        """Test d'envoi de spam réussi"""
        mock_response = Mock()
        mock_response.json.return_value = {"success": True, "message": "Spam sent"}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = self.api_client.spam_user("123")
        
        self.assertIsNotNone(result)
        self.assertTrue(result["success"])
    
    @patch('api_client.requests.Session.get')
    def test_get_events_success(self, mock_get):
        """Test de récupération d'événements réussie"""
        mock_response = Mock()
        mock_response.json.return_value = {"events": [{"id": 1, "name": "Event 1"}]}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = self.api_client.get_events("ME")
        
        self.assertIsNotNone(result)
        self.assertIn("events", result)
    
    @patch('api_client.requests.Session.get')
    def test_get_info_success(self, mock_get):
        """Test de récupération d'informations réussie"""
        mock_response = Mock()
        mock_response.json.return_value = {"version": "1.0", "status": "online"}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = self.api_client.get_info()
        
        self.assertIsNotNone(result)
        self.assertEqual(result["version"], "1.0")

def run_tests():
    """Exécuter tous les tests"""
    print("🧪 Démarrage des tests du système de bots...\n")
    
    # Créer une suite de tests
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Ajouter les tests
    suite.addTests(loader.loadTestsFromTestCase(TestBotManager))
    suite.addTests(loader.loadTestsFromTestCase(TestAPIClient))
    
    # Exécuter les tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Résumé
    print(f"\n📊 Résultats des tests:")
    print(f"✅ Tests réussis: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ Tests échoués: {len(result.failures)}")
    print(f"🚨 Erreurs: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ Échecs:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n🚨 Erreurs:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_tests()
    exit(0 if success else 1)
