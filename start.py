#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de démarrage pour le système de bots
"""

import os
import sys
import logging
from config import MASTER_BOT_TOKEN, ADMIN_USER_ID

def check_configuration():
    """Vérifier la configuration avant le démarrage"""
    print("🔍 Vérification de la configuration...")
    
    errors = []
    
    # Vérifier le fichier .env
    if not os.path.exists('.env'):
        errors.append("❌ Fichier .env manquant. Copiez .env.example vers .env et configurez-le.")
    
    # Vérifier les variables d'environnement
    if not MASTER_BOT_TOKEN:
        errors.append("❌ MASTER_BOT_TOKEN non configuré dans .env")
    
    if not ADMIN_USER_ID:
        errors.append("❌ ADMIN_USER_ID non configuré dans .env")
    
    # Vérifier les dossiers
    required_dirs = ['logs', 'bots']
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            print(f"📁 Création du dossier {dir_name}...")
            os.makedirs(dir_name, exist_ok=True)
    
    if errors:
        print("\n🚨 Erreurs de configuration détectées:")
        for error in errors:
            print(f"  {error}")
        print("\n📋 Pour configurer le système:")
        print("1. Copiez .env.example vers .env")
        print("2. Éditez .env avec vos tokens et IDs")
        print("3. Relancez ce script")
        return False
    
    print("✅ Configuration valide!")
    return True

def install_dependencies():
    """Installer les dépendances si nécessaire"""
    print("📦 Vérification des dépendances...")
    
    try:
        import telebot
        import requests
        import dotenv
        print("✅ Toutes les dépendances sont installées!")
        return True
    except ImportError as e:
        print(f"❌ Dépendance manquante: {e}")
        print("📥 Installation des dépendances...")
        
        import subprocess
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
            print("✅ Dépendances installées avec succès!")
            return True
        except subprocess.CalledProcessError:
            print("❌ Erreur lors de l'installation des dépendances")
            return False

def show_welcome():
    """Afficher le message de bienvenue"""
    print("""
🤖 ===============================================
   BOT CRÉATEUR DE BOTS - SYSTÈME DE GESTION
===============================================

📋 Fonctionnalités:
  • Création automatique de bots Telegram
  • Gestion centralisée des bots
  • APIs intégrées (check, spam, events, info)
  • Interface d'administration complète

🚀 Le bot maître va démarrer...
""")

def main():
    """Fonction principale"""
    show_welcome()
    
    # Vérifier la configuration
    if not check_configuration():
        sys.exit(1)
    
    # Installer les dépendances
    if not install_dependencies():
        sys.exit(1)
    
    print("🚀 Démarrage du Bot Maître...")
    print(f"👤 Admin User ID: {ADMIN_USER_ID}")
    print("📱 Envoyez /start au bot pour commencer!")
    print("🛑 Appuyez sur Ctrl+C pour arrêter\n")
    
    # Démarrer le bot maître
    try:
        from master_bot import bot
        bot.infinity_polling()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du système...")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
