2025-07-27 21:16:15,521 - bot_manager - INFO - <PERSON><PERSON><PERSON> 0 bots depuis le fichier
2025-07-27 21:16:58,959 - bot_manager - INFO - Chargé 0 bots depuis le fichier
2025-07-27 21:16:58,959 - __main__ - INFO - Démarrage du Bot Maître
2025-07-27 21:16:58,959 - __main__ - INFO - Admin User ID: 7763623565
2025-07-27 21:20:25,978 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 608
2025-07-27 21:20:25,979 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\__init__.py", line 1074, in __threaded_polling
    self.worker_pool.raise_exceptions()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\util.py", line 147, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\__init__.py", line 6801, in _run_middlewares_and_handler
    result = handler['function'](message)
  File "C:\Users\<USER>\Desktop\parfume\master_bot.py", line 75, in start_command
    bot.reply_to(message, welcome_text, parse_mode='Markdown')
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\__init__.py", line 4528, in reply_to
    return self.send_message(message.chat.id, text, reply_to_message_id=message.message_id, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\__init__.py", line 1549, in send_message
    apihelper.send_message(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\apihelper.py", line 264, in send_message
    return _make_request(token, method_url, params=payload, method='post')
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 608

2025-07-27 21:20:51,527 - bot_manager - INFO - Chargé 0 bots depuis le fichier
2025-07-27 21:20:52,157 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 608
2025-07-27 21:20:52,159 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\__init__.py", line 1074, in __threaded_polling
    self.worker_pool.raise_exceptions()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\util.py", line 147, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\__init__.py", line 6801, in _run_middlewares_and_handler
    result = handler['function'](message)
  File "C:\Users\<USER>\Desktop\parfume\master_bot.py", line 75, in start_command
    bot.reply_to(message, welcome_text, parse_mode='Markdown')
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\__init__.py", line 4528, in reply_to
    return self.send_message(message.chat.id, text, reply_to_message_id=message.message_id, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\__init__.py", line 1549, in send_message
    apihelper.send_message(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\apihelper.py", line 264, in send_message
    return _make_request(token, method_url, params=payload, method='post')
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 608

2025-07-27 21:21:25,737 - bot_manager - INFO - Chargé 0 bots depuis le fichier
2025-07-27 21:21:25,737 - __main__ - INFO - Démarrage du Bot Maître
2025-07-27 21:21:25,738 - __main__ - INFO - Admin User ID: 7763623565
2025-07-27 21:23:18,503 - bot_manager - INFO - Chargé 0 bots depuis le fichier
2025-07-27 21:23:18,503 - __main__ - INFO - Démarrage du Bot Maître
2025-07-27 21:23:18,504 - __main__ - INFO - Admin User ID: 7763623565
2025-07-27 21:24:15,047 - bot_manager - INFO - Chargé 0 bots depuis le fichier
2025-07-27 21:25:09,124 - bot_manager - INFO - Chargé 0 bots depuis le fichier
2025-07-27 21:25:09,124 - __main__ - INFO - Démarrage du Bot Maître
2025-07-27 21:25:09,125 - __main__ - INFO - Admin User ID: 7763623565
2025-07-27 21:25:09,595 - bot_manager - INFO - Données des bots sauvegardées
2025-07-27 21:25:09,599 - bot_manager - INFO - Bot créé: souhail (ID: 0a3acd94-e692-4ac7-b52c-75fa41a7f2c8)
