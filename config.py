import os
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

# Configuration du bot principal
MASTER_BOT_TOKEN = os.getenv('MASTER_BOT_TOKEN')
ADMIN_USER_ID = os.getenv('ADMIN_USER_ID')

# APIs externes
API_BASE_URLS = {
    'check_status': 'https://ch9ayfa-check-1.vercel.app/check_status',
    'spam': 'https://spam-ch9ayfa.vercel.app/spam',
    'events': 'https://eventes-ch9ayfa.vercel.app/eventes',
    'info': 'https://info-ch9ayfa.vercel.app/2511293320'
}

# Clé API
API_KEY = 'ch9ayfa'

# Configuration des bots
MAX_BOTS = 10  # Nombre maximum de bots secondaires
BOT_DATA_FILE = 'bots_data.json'
LOGS_DIR = 'logs'

# Configuration des logs
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# Région par défaut pour les événements
DEFAULT_REGION = 'ME'
